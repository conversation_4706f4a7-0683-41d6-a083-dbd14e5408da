<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useToast } from "primevue/usetoast";
import { login } from "../services/auth";

const router = useRouter();
const toast = useToast();

const email = ref("");
const password = ref("");
const loading = ref(false);
const emailError = ref("");
const passwordError = ref("");

const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email.value) {
    emailError.value = "请输入邮箱地址";
    return false;
  }
  if (!emailRegex.test(email.value)) {
    emailError.value = "请输入有效的邮箱地址";
    return false;
  }
  emailError.value = "";
  return true;
};

const validatePassword = () => {
  if (!password.value) {
    passwordError.value = "请输入密码";
    return false;
  }
  if (password.value.length < 6) {
    passwordError.value = "密码长度至少为6位";
    return false;
  }
  passwordError.value = "";
  return true;
};

const passwordStrength = computed(() => {
  if (!password.value) return 0;
  let strength = 0;
  if (password.value.length >= 8) strength++;
  if (/[A-Z]/.test(password.value)) strength++;
  if (/[a-z]/.test(password.value)) strength++;
  if (/[0-9]/.test(password.value)) strength++;
  if (/[^A-Za-z0-9]/.test(password.value)) strength++;
  return strength;
});

const strengthClass = computed(() => {
  const strength = passwordStrength.value;
  if (strength <= 1) return "weak";
  if (strength <= 3) return "medium";
  return "strong";
});

const handleLogin = async () => {
  const isEmailValid = validateEmail();
  const isPasswordValid = validatePassword();

  if (!isEmailValid || !isPasswordValid) {
    return;
  }

  try {
    loading.value = true;
    const response = await login(email.value, password.value);

    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "登录成功",
        life: 3000,
      });
      router.push("/");
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message,
        life: 3000,
      });
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "登录失败，请稍后重试",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

const goToForgotPassword = () => {
  router.push("/forgot-password");
};
</script>

<template>
  <div class="login-container">
    <!-- 左侧图片区域 -->
    <div class="login-visual">
      <div class="visual-content">
        <div class="brand-logo">
          <div class="logo-icon">
            <i
              class="pi pi-chart-line"
              style="font-size: 4rem;"
            ></i>
          </div>
          <h1 class="brand-title">账务管理系统</h1>
          <p class="brand-subtitle">专业 · 高效 · 安全</p>
        </div>
        <div class="visual-illustration">
          <!-- 使用CSS创建的简洁插图 -->
          <div class="illustration-container">
            <div class="chart-bars">
              <div class="bar bar-1"></div>
              <div class="bar bar-2"></div>
              <div class="bar bar-3"></div>
              <div class="bar bar-4"></div>
            </div>
            <div class="floating-elements">
              <div class="element element-1"></div>
              <div class="element element-2"></div>
              <div class="element element-3"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录表单区域 -->
    <div class="login-form-section">
      <div class="form-container">
        <div class="form-header">
          <h2 class="form-title">欢迎回来</h2>
          <p class="form-subtitle">请登录您的账户</p>
        </div>

        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-field">
            <label for="email" class="field-label">邮箱地址</label>
            <InputText
              id="email"
              v-model="email"
              type="email"
              placeholder="请输入您的邮箱地址"
              @blur="validateEmail"
              :class="{ 'p-invalid': emailError }"
              required
            />
            <small class="error-message" v-if="emailError">{{
              emailError
            }}</small>
          </div>

          <div class="form-field">
            <label for="password" class="field-label">密码</label>
            <Password
              id="password"
              class="w-full"
              v-model="password"
              placeholder="请输入您的密码"
              :feedback="false"
              toggleMask
              @blur="validatePassword"
              :class="{ 'p-invalid': passwordError }"
              required
            />
            <small class="error-message" v-if="passwordError">{{
              passwordError
            }}</small>
            <div class="password-strength" v-if="password">
              <div class="strength-indicator" :class="strengthClass"></div>
            </div>
          </div>

          <div class="form-actions">
            <Button
              type="button"
              label="忘记密码？"
              link
              @click="goToForgotPassword"
              class="forgot-password-btn"
            />
          </div>

          <Button
            type="submit"
            label="登录"
            :loading="loading"
            class="login-btn"
            size="large"
          />
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 主容器 */
.login-container {
  min-height: 100vh;
  display: flex;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 左侧视觉区域 */
.login-visual {
  flex: 3;
  background: linear-gradient(135deg, #7bead6 0%, #56d6b4 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.visual-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
}

.brand-logo {
  margin-bottom: 3rem;
}

.logo-icon {
  margin-bottom: 1.5rem;
  animation: float 3s ease-in-out infinite;
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.02em;
}

.brand-subtitle {
  font-size: 1.125rem;
  font-weight: 400;
  margin: 0;
  opacity: 0.9;
  letter-spacing: 0.05em;
}

/* 插图容器 */
.illustration-container {
  position: relative;
  width: 300px;
  height: 200px;
  margin: 0 auto;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 12px;
  height: 120px;
}

.bar {
  width: 24px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  animation: barGrow 2s ease-in-out infinite;
}

.bar-1 {
  height: 60px;
  animation-delay: 0s;
}
.bar-2 {
  height: 80px;
  animation-delay: 0.2s;
}
.bar-3 {
  height: 100px;
  animation-delay: 0.4s;
}
.bar-4 {
  height: 70px;
  animation-delay: 0.6s;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.element {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: float 4s ease-in-out infinite;
}

.element-1 {
  width: 20px;
  height: 20px;
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.element-2 {
  width: 16px;
  height: 16px;
  top: 60%;
  right: 25%;
  animation-delay: 1s;
}

.element-3 {
  width: 12px;
  height: 12px;
  bottom: 30%;
  left: 15%;
  animation-delay: 2s;
}

/* 右侧表单区域 */
.login-form-section {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: #ffffff;
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.form-title {
  font-size: 2rem;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.02em;
}

.form-subtitle {
  font-size: 1rem;
  color: #86868b;
  margin: 0;
  font-weight: 400;
}

.login-form {
  width: 100%;
}

.form-field {
  margin-bottom: 1.5rem;
}

.field-label {
  display: block;
  margin-bottom: 0.5rem;
  color: #1d1d1f;
  font-weight: 500;
  font-size: 0.875rem;
}

/* 输入框样式 - 苹果风格 */
.form-field :deep(.p-inputtext),
.form-field :deep(.p-password-input) {
  width: 100%;
  height: 48px;
  border: 1px solid #d2d2d7;
  border-radius: 8px;
  padding: 0 16px;
  font-size: 1rem;
  background: #ffffff;
  transition: all 0.2s ease;
}

.form-field :deep(.p-inputtext:enabled:hover),
.form-field :deep(.p-password-input:enabled:hover) {
  border-color: #007aff;
}

.form-field :deep(.p-inputtext:enabled:focus),
.form-field :deep(.p-password-input:enabled:focus) {
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  outline: none;
}

.form-field :deep(.p-invalid) {
  border-color: #ff3b30;
}

.error-message {
  color: #ff3b30;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 0.5rem;
  height: 3px;
  border-radius: 1.5px;
  background-color: #f2f2f7;
  overflow: hidden;
}

.strength-indicator {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 1.5px;
}

.strength-indicator.weak {
  width: 33.33%;
  background-color: #ff3b30;
}

.strength-indicator.medium {
  width: 66.66%;
  background-color: #ff9500;
}

.strength-indicator.strong {
  width: 100%;
  background-color: #34c759;
}

.form-actions {
  text-align: center;
  margin-bottom: 1.5rem;
}

.forgot-password-btn :deep(.p-button-label) {
  font-size: 0.875rem;
}

/* 登录按钮 - 苹果风格 */
.login-btn {
  width: 100%;
  height: 48px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  color: white;
  transition: all 0.2s ease;
  cursor: pointer;
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn:disabled {
  background: #d2d2d7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 动画效果 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes barGrow {
  0%,
  100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }

  .login-visual {
    min-height: 40vh;
  }

  .brand-title {
    font-size: 2rem;
  }

  .illustration-container {
    width: 250px;
    height: 150px;
  }

  .login-form-section {
    padding: 1.5rem;
  }

  .form-container {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .login-visual {
    min-height: 35vh;
  }

  .brand-title {
    font-size: 1.75rem;
  }

  .form-title {
    font-size: 1.75rem;
  }

  .login-form-section {
    padding: 1rem;
  }
}
</style>
