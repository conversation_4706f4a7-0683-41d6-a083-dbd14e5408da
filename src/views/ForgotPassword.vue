<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useToast } from "primevue/usetoast";

const router = useRouter();
const toast = useToast();

const email = ref("");
const newPassword = ref("");
const confirmPassword = ref("");
const verificationCode = ref("");
const showResetForm = ref(false);
const emailError = ref("");
const passwordError = ref("");
const confirmPasswordError = ref("");
const verificationCodeError = ref("");

const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email.value) {
    emailError.value = "请输入邮箱地址";
    return false;
  }
  if (!emailRegex.test(email.value)) {
    emailError.value = "请输入有效的邮箱地址";
    return false;
  }
  emailError.value = "";
  return true;
};

const validatePassword = () => {
  if (!newPassword.value) {
    passwordError.value = "请输入新密码";
    return false;
  }
  if (newPassword.value.length < 6) {
    passwordError.value = "密码长度至少为6位";
    return false;
  }
  passwordError.value = "";
  return true;
};

const validateConfirmPassword = () => {
  if (!confirmPassword.value) {
    confirmPasswordError.value = "请确认新密码";
    return false;
  }
  if (confirmPassword.value !== newPassword.value) {
    confirmPasswordError.value = "两次输入的密码不一致";
    return false;
  }
  confirmPasswordError.value = "";
  return true;
};

const validateVerificationCode = () => {
  if (!verificationCode.value) {
    verificationCodeError.value = "请输入验证码";
    return false;
  }
  if (!/^\d{6}$/.test(verificationCode.value)) {
    verificationCodeError.value = "验证码格式不正确";
    return false;
  }
  verificationCodeError.value = "";
  return true;
};

const passwordStrength = computed(() => {
  if (!newPassword.value) return 0;
  let strength = 0;
  if (newPassword.value.length >= 8) strength++;
  if (/[A-Z]/.test(newPassword.value)) strength++;
  if (/[a-z]/.test(newPassword.value)) strength++;
  if (/[0-9]/.test(newPassword.value)) strength++;
  if (/[^A-Za-z0-9]/.test(newPassword.value)) strength++;
  return strength;
});

const strengthClass = computed(() => {
  const strength = passwordStrength.value;
  if (strength <= 1) return "weak";
  if (strength <= 3) return "medium";
  return "strong";
});

const requestReset = () => {
  if (!validateEmail()) {
    return;
  }

  // TODO: 实现发送验证码逻辑
  toast.add({
    severity: "success",
    summary: "成功",
    detail: "验证码已发送到您的邮箱",
    life: 3000,
  });
  showResetForm.value = true;
};

const resetPassword = () => {
  const isEmailValid = validateEmail();
  const isPasswordValid = validatePassword();
  const isConfirmPasswordValid = validateConfirmPassword();
  const isVerificationCodeValid = validateVerificationCode();

  if (
    !isEmailValid ||
    !isPasswordValid ||
    !isConfirmPasswordValid ||
    !isVerificationCodeValid
  ) {
    return;
  }

  // TODO: 实现重置密码逻辑
  toast.add({
    severity: "success",
    summary: "成功",
    detail: "密码重置成功",
    life: 3000,
  });
  router.push("/login");
};

const goToLogin = () => {
  router.push("/login");
};
</script>

<template>
  <div class="forgot-password-container">
    <!-- 左侧图片区域 -->
    <div class="forgot-visual">
      <div class="visual-content">
        <div class="brand-logo">
          <div class="logo-icon">
            <i class="pi pi-shield" style="font-size: 3rem; color: #ddd332"></i>
          </div>
          <h1 class="brand-title">密码重置</h1>
          <p class="brand-subtitle">安全 · 快速 · 可靠</p>
        </div>
        <div class="visual-illustration">
          <!-- 使用CSS创建的安全主题插图 -->
          <div class="illustration-container">
            <div class="security-elements">
              <div class="shield-icon">
                <div class="shield-body"></div>
                <div class="shield-check"></div>
              </div>
              <div class="lock-elements">
                <div class="lock lock-1"></div>
                <div class="lock lock-2"></div>
                <div class="lock lock-3"></div>
              </div>
            </div>
            <div class="floating-elements">
              <div class="element element-1"></div>
              <div class="element element-2"></div>
              <div class="element element-3"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧表单区域 -->
    <div class="forgot-form-section">
      <div class="form-container">
        <div class="form-header">
          <h2 class="form-title">
            {{ showResetForm ? "设置新密码" : "重置密码" }}
          </h2>
          <p class="form-subtitle">
            {{
              showResetForm
                ? "请输入您的新密码"
                : "请输入您的邮箱地址，我们将发送验证码"
            }}
          </p>
        </div>

        <form
          @submit.prevent="showResetForm ? resetPassword() : requestReset()"
          class="forgot-form"
        >
          <div class="form-field">
            <label for="email" class="field-label">邮箱地址</label>
            <InputText
              id="email"
              v-model="email"
              type="email"
              placeholder="请输入您的邮箱地址"
              :disabled="showResetForm"
              @blur="validateEmail"
              :class="{ 'p-invalid': emailError }"
              required
            />
            <small class="error-message" v-if="emailError">{{
              emailError
            }}</small>
          </div>

          <template v-if="showResetForm">
            <div class="form-field">
              <label for="verificationCode" class="field-label">验证码</label>
              <InputText
                id="verificationCode"
                v-model="verificationCode"
                placeholder="请输入6位验证码"
                @blur="validateVerificationCode"
                :class="{ 'p-invalid': verificationCodeError }"
                maxlength="6"
                required
              />
              <small class="error-message" v-if="verificationCodeError">{{
                verificationCodeError
              }}</small>
            </div>

            <div class="form-field">
              <label for="newPassword" class="field-label">新密码</label>
              <Password
                id="newPassword"
                v-model="newPassword"
                placeholder="请输入新密码"
                :feedback="false"
                toggleMask
                @blur="validatePassword"
                :class="{ 'p-invalid': passwordError }"
                required
                class="w-full"
              />
              <small class="error-message" v-if="passwordError">{{
                passwordError
              }}</small>
              <div class="password-strength" v-if="newPassword">
                <div class="strength-indicator" :class="strengthClass"></div>
              </div>
            </div>

            <div class="form-field">
              <label for="confirmPassword" class="field-label">确认密码</label>
              <Password
                id="confirmPassword"
                v-model="confirmPassword"
                placeholder="请再次输入新密码"
                :feedback="false"
                toggleMask
                @blur="validateConfirmPassword"
                :class="{ 'p-invalid': confirmPasswordError }"
                required
                class="w-full"
              />
              <small class="error-message" v-if="confirmPasswordError">{{
                confirmPasswordError
              }}</small>
            </div>
          </template>

          <div class="form-actions">
            <Button
              type="button"
              label="返回登录"
              link
              @click="goToLogin"
              class="back-login-btn"
            />
          </div>

          <Button
            type="submit"
            :label="showResetForm ? '重置密码' : '发送验证码'"
            class="submit-btn"
            size="large"
          />
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 主容器 - 苹果风格的分屏布局 */
.forgot-password-container {
  min-height: 100vh;
  display: flex;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 左侧视觉区域 */
.forgot-visual {
  flex: 3;
  background: linear-gradient(135deg, #FF9500 0%, #FF6B35 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.visual-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
}

.brand-logo {
  margin-bottom: 3rem;
}

.logo-icon {
  margin-bottom: 1.5rem;
  animation: float 3s ease-in-out infinite;
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.02em;
}

.brand-subtitle {
  font-size: 1.125rem;
  font-weight: 400;
  margin: 0;
  opacity: 0.9;
  letter-spacing: 0.05em;
}

/* 安全主题插图容器 */
.illustration-container {
  position: relative;
  width: 300px;
  height: 200px;
  margin: 0 auto;
}

.security-elements {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
  position: relative;
}

.shield-icon {
  position: relative;
  width: 80px;
  height: 100px;
  margin: 0 auto;
}

.shield-body {
  width: 80px;
  height: 100px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 40px 40px 0 40px;
  position: relative;
  animation: pulse 2s ease-in-out infinite;
}

.shield-check {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 10px;
  border: 3px solid white;
  border-top: none;
  border-right: none;
  transform: translate(-50%, -60%) rotate(-45deg);
  animation: checkmark 1.5s ease-in-out infinite;
}

.lock-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.lock {
  position: absolute;
  width: 16px;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  animation: lockFloat 3s ease-in-out infinite;
}

.lock::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 3px;
  width: 10px;
  height: 10px;
  border: 2px solid rgba(255, 255, 255, 0.4);
  border-bottom: none;
  border-radius: 50% 50% 0 0;
}

.lock-1 {
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.lock-2 {
  top: 30%;
  right: 20%;
  animation-delay: 1s;
}

.lock-3 {
  bottom: 25%;
  left: 25%;
  animation-delay: 2s;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.element {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: float 4s ease-in-out infinite;
}

.element-1 {
  width: 20px;
  height: 20px;
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.element-2 {
  width: 16px;
  height: 16px;
  top: 60%;
  right: 25%;
  animation-delay: 1s;
}

.element-3 {
  width: 12px;
  height: 12px;
  bottom: 30%;
  left: 15%;
  animation-delay: 2s;
}

/* 右侧表单区域 */
.forgot-form-section {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: #ffffff;
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.form-title {
  font-size: 2rem;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.02em;
}

.form-subtitle {
  font-size: 1rem;
  color: #86868b;
  margin: 0;
  font-weight: 400;
  line-height: 1.4;
}

.forgot-form {
  width: 100%;
}

.form-field {
  margin-bottom: 1.5rem;
}

.field-label {
  display: block;
  margin-bottom: 0.5rem;
  color: #1d1d1f;
  font-weight: 500;
  font-size: 0.875rem;
}

/* 输入框样式 - 苹果风格 */
.form-field :deep(.p-inputtext),
.form-field :deep(.p-password-input) {
  width: 100%;
  height: 48px;
  border: 1px solid #d2d2d7;
  border-radius: 8px;
  padding: 0 16px;
  font-size: 1rem;
  background: #ffffff;
  transition: all 0.2s ease;
}

.form-field :deep(.p-inputtext:enabled:hover),
.form-field :deep(.p-password-input:enabled:hover) {
  border-color: #FF9500;
}

.form-field :deep(.p-inputtext:enabled:focus),
.form-field :deep(.p-password-input:enabled:focus) {
  border-color: #FF9500;
  box-shadow: 0 0 0 3px rgba(255, 149, 0, 0.1);
  outline: none;
}

.form-field :deep(.p-inputtext:disabled) {
  background-color: #f2f2f7;
  color: #86868b;
  cursor: not-allowed;
}

.form-field :deep(.p-invalid) {
  border-color: #FF3B30;
}

.error-message {
  color: #FF3B30;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 0.5rem;
  height: 3px;
  border-radius: 1.5px;
  background-color: #f2f2f7;
  overflow: hidden;
}

.strength-indicator {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 1.5px;
}

.strength-indicator.weak {
  width: 33.33%;
  background-color: #FF3B30;
}

.strength-indicator.medium {
  width: 66.66%;
  background-color: #FF9500;
}

.strength-indicator.strong {
  width: 100%;
  background-color: #34C759;
}

.form-actions {
  text-align: center;
  margin-bottom: 1.5rem;
}

.back-login-btn :deep(.p-button-label) {
  color: #FF9500;
  font-size: 0.875rem;
}

.back-login-btn:hover :deep(.p-button-label) {
  color: #E6850E;
}

/* 提交按钮 - 苹果风格 */
.submit-btn {
  width: 100%;
  height: 48px;
  background: #FF9500;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  color: white;
  transition: all 0.2s ease;
  cursor: pointer;
}

.submit-btn:hover {
  background: #E6850E;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 149, 0, 0.3);
}

.submit-btn:active {
  transform: translateY(0);
}

.submit-btn:disabled {
  background: #d2d2d7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes checkmark {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes lockFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-8px) rotate(5deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .forgot-password-container {
    flex-direction: column;
  }

  .forgot-visual {
    min-height: 40vh;
  }

  .brand-title {
    font-size: 2rem;
  }

  .illustration-container {
    width: 250px;
    height: 150px;
  }

  .forgot-form-section {
    padding: 1.5rem;
  }

  .form-container {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .forgot-visual {
    min-height: 35vh;
  }

  .brand-title {
    font-size: 1.75rem;
  }

  .form-title {
    font-size: 1.75rem;
  }

  .forgot-form-section {
    padding: 1rem;
  }
}
</style>
